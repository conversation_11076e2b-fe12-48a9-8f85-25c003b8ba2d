#!/bin/bash

# Nginx HTTPS 服务启动脚本
# 基于 Nginx + PHP-FPM 架构
#
# 使用方法:
#   ./start_nginx_https.sh                # 正常启动
#   ./start_nginx_https.sh --disable-selinux  # 永久禁用SELinux后启动

# 检查是否需要禁用SELinux
DISABLE_SELINUX=false
if [[ "$1" == "--disable-selinux" ]]; then
    DISABLE_SELINUX=true
fi

# 颜色输出函数
print_status() {
    echo -e "\033[1;32m[INFO]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[1;33m[WARNING]\033[0m $1"
}

print_header() {
    echo -e "\033[1;36m========================================\033[0m"
    echo -e "\033[1;36m  Nginx HTTPS 测试服务启动脚本\033[0m"
    echo -e "\033[1;36m  基于 Nginx + PHP-FPM 架构\033[0m"
    echo -e "\033[1;36m========================================\033[0m"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查并安装依赖
install_dependencies() {
    print_status "检查并安装依赖包..."
    
    # 检查Nginx
    if ! command -v nginx &> /dev/null; then
        print_status "安装 Nginx..."
        yum install -y nginx
    else
        print_status "Nginx 已安装"
    fi
    
    # 检查PHP-FPM
    if ! command -v php-fpm &> /dev/null; then
        print_status "安装 PHP-FPM..."
        yum install -y php php-fpm php-json php-mbstring
    else
        print_status "PHP-FPM 已安装"
    fi
    
    # 检查OpenSSL
    if ! command -v openssl &> /dev/null; then
        print_status "安装 OpenSSL..."
        yum install -y openssl
    else
        print_status "OpenSSL 已安装"
    fi
}

# 生成SSL证书
generate_ssl_cert() {
    CERT_DIR="/root/ebpf/nginx-https-server"
    CERT_FILE="$CERT_DIR/server.crt"
    KEY_FILE="$CERT_DIR/server.key"
    
    if [[ ! -f "$CERT_FILE" || ! -f "$KEY_FILE" ]]; then
        print_status "生成自签名SSL证书..."
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout "$KEY_FILE" \
            -out "$CERT_FILE" \
            -subj "/C=CN/ST=Test/L=Test/O=TestOrg/CN=localhost"
        
        # 设置证书文件权限
        chmod 600 "$KEY_FILE"
        chmod 644 "$CERT_FILE"
        print_status "SSL证书生成完成"
    else
        print_status "SSL证书已存在"
    fi
}

# 配置PHP-FPM
configure_php_fpm() {
    print_status "配置 PHP-FPM..."
    
    # 备份原配置
    if [[ -f /etc/php-fpm.d/www.conf ]]; then
        cp /etc/php-fpm.d/www.conf /etc/php-fpm.d/www.conf.backup
    fi
    
    # 确保PHP-FPM监听正确端口
    sed -i 's/listen = \/run\/php-fpm\/www\.sock/listen = 127.0.0.1:9000/' /etc/php-fpm.d/www.conf 2>/dev/null || true
    
    # 启动PHP-FPM
    systemctl enable php-fpm
    systemctl start php-fpm
    
    if systemctl is-active --quiet php-fpm; then
        print_status "PHP-FPM 启动成功"
    else
        print_error "PHP-FPM 启动失败"
        exit 1
    fi
}

# 配置Nginx
configure_nginx() {
    print_status "配置 Nginx..."
    
    NGINX_CONF="/root/ebpf/nginx-https-server/nginx.conf"
    
    if [[ ! -f "$NGINX_CONF" ]]; then
        print_error "Nginx 配置文件不存在: $NGINX_CONF"
        exit 1
    fi
    
    # 测试Nginx配置
    nginx -t -c "$NGINX_CONF"
    if [[ $? -ne 0 ]]; then
        print_error "Nginx 配置文件有错误"
        exit 1
    fi
    
    print_status "Nginx 配置文件验证通过"
}

# 创建日志目录
create_log_dirs() {
    print_status "创建日志目录..."
    mkdir -p /var/log/nginx
    touch /var/log/nginx/access.log
    touch /var/log/nginx/error.log
    touch /var/log/nginx/api.log
    
    # 设置权限
    chmod 644 /var/log/nginx/*.log
    chown nginx:nginx /var/log/nginx/*.log 2>/dev/null || true
}

# 配置SELinux和文件权限
configure_selinux() {
    print_status "配置SELinux和文件权限..."
    
    # 强制检查并处理SELinux
    print_status "彻底处理SELinux问题..."
    
    # 1. 临时禁用SELinux（如果还在运行）
    if command -v getenforce >/dev/null 2>&1; then
        SELINUX_STATUS=$(getenforce 2>/dev/null || echo "Disabled")
        print_status "当前SELinux状态: $SELINUX_STATUS"
        
        if [[ "$SELINUX_STATUS" != "Disabled" ]]; then
            print_status "强制禁用SELinux..."
            setenforce 0 2>/dev/null || true
            print_status "SELinux已临时禁用"
        fi
    fi
    
    # 2. 永久禁用SELinux配置
    if [[ -f /etc/selinux/config ]]; then
        print_status "确保SELinux永久禁用..."
        sed -i 's/^SELINUX=.*/SELINUX=disabled/' /etc/selinux/config
        print_status "SELinux配置已设为永久禁用"
    fi
    
    # 3. 设置文件权限 - 更宽松的权限设置
    print_status "设置文件权限..."
    
    # 设置工作目录权限
    chmod -R 755 /root/ebpf/nginx-https-server/
    
    # 特殊处理SSL证书权限
    chmod 644 /root/ebpf/nginx-https-server/server.crt 2>/dev/null || true
    chmod 600 /root/ebpf/nginx-https-server/server.key 2>/dev/null || true
    
    # 设置网页文件权限
    chmod -R 644 /root/ebpf/nginx-https-server/html/* 2>/dev/null || true
    chmod -R 644 /root/ebpf/nginx-https-server/api/* 2>/dev/null || true
    
    # 设置目录权限
    find /root/ebpf/nginx-https-server/ -type d -exec chmod 755 {} \; 2>/dev/null || true
    
    # 4. 清除可能的SELinux上下文
    print_status "清除SELinux上下文..."
    if command -v restorecon >/dev/null 2>&1; then
        restorecon -R /root/ebpf/nginx-https-server/ 2>/dev/null || true
    fi
    
    # 5. 设置所有者（使用root，避免nginx用户权限问题）
    chown -R root:root /root/ebpf/nginx-https-server/ 2>/dev/null || true
    
    # 6. 确保日志目录权限
    mkdir -p /var/log/nginx
    chmod 755 /var/log/nginx
    touch /var/log/nginx/access.log /var/log/nginx/error.log /var/log/nginx/api.log
    chmod 644 /var/log/nginx/*.log
    
    print_status "权限设置完成"
    
    # 7. 最终验证
    print_status "验证关键文件权限..."
    ls -la /root/ebpf/nginx-https-server/server.* 2>/dev/null || true
    ls -la /root/ebpf/nginx-https-server/nginx.conf
}

# 完全禁用SELinux（可选）
disable_selinux_permanently() {
    print_warning "正在永久禁用SELinux..."
    
    # 临时禁用
    setenforce 0 2>/dev/null || true
    
    # 永久禁用
    if [[ -f /etc/selinux/config ]]; then
        sed -i 's/^SELINUX=.*/SELINUX=disabled/' /etc/selinux/config
        print_status "SELinux已永久禁用（重启后生效）"
    fi
    
    print_status "SELinux当前状态: $(getenforce 2>/dev/null || echo '已禁用')"
}

# 启动Nginx服务
start_nginx() {
    print_status "启动 Nginx 服务..."
    
    NGINX_CONF="/root/ebpf/nginx-https-server/nginx.conf"
    
    # 简单直接的启动方式
    print_status "启动Nginx..."
    nginx -c "$NGINX_CONF"
    
    # 短暂等待
    sleep 1
    
    # 检查nginx是否在运行
    if pgrep nginx > /dev/null; then
        print_status "✅ Nginx启动成功"
        return 0
    else
        print_error "❌ Nginx启动失败"
        tail -5 /var/log/nginx/error.log 2>/dev/null || true
        return 1
    fi
}

# 检查服务状态
check_services() {
    print_status "检查服务状态..."
    
    # 检查Nginx进程
    if pgrep nginx > /dev/null; then
        print_status "✅ Nginx 进程运行正常"
    else
        print_error "❌ Nginx 进程未运行"
        return 1
    fi
    
    # 检查PHP-FPM进程
    if systemctl is-active --quiet php-fpm; then
        print_status "✅ PHP-FPM 服务运行正常"
    else
        print_error "❌ PHP-FPM 服务未运行"
        return 1
    fi
    
    # 检查端口监听
    if netstat -tlnp | grep :9443 > /dev/null 2>&1; then
        print_status "✅ 端口 9443 监听正常"
    else
        print_warning "⚠️  端口 9443 可能未正常监听"
    fi
    
    if netstat -tlnp | grep :9000 > /dev/null 2>&1; then
        print_status "✅ PHP-FPM 端口 9000 监听正常"
    else
        print_warning "⚠️  PHP-FPM 端口 9000 可能未正常监听"
    fi
}

# 显示访问信息
show_access_info() {
    echo ""
    print_header
    print_status "🚀 Nginx HTTPS 服务启动完成！"
    echo ""
    print_status "📋 服务信息:"
    echo "   • 服务器类型: Nginx + PHP-FPM"
    echo "   • HTTPS端口: 9443"
    echo "   • SSL证书: 自签名证书"
    echo "   • 文档根目录: /root/ebpf/nginx-https-server/html"
    echo ""
    print_status "🌐 访问地址:"
    echo "   • 主页面: https://localhost:9443"
    echo "   • API接口: https://localhost:9443/api/test"
    echo ""
    print_status "🔗 可用API接口 (共10个):"
    echo "   • GET  /api/test - 基础测试接口"
    echo "   • POST /api/complex - 复杂数据处理"
    echo "   • GET  /api/users - 用户管理接口"
    echo "   • GET  /api/data - 大数据接口 (>2KB)"
    echo "   • GET  /api/status - 系统状态接口"
    echo "   • GET  /api/config - 配置管理接口"
    echo "   • GET  /api/log - 日志接口 (>2KB)"
    echo "   • GET  /api/metrics - 监控指标接口"
    echo "   • GET  /api/search - 搜索接口"
    echo ""
    print_status "📝 日志文件:"
    echo "   • 访问日志: /var/log/nginx/access.log"
    echo "   • 错误日志: /var/log/nginx/error.log"
    echo "   • API日志: /var/log/nginx/api.log"
    echo ""
    print_status "🛠️  管理命令:"
    echo "   • 停止服务: ./stop_nginx_https.sh"
    echo "   • 测试API: ./test_apis.sh"
    echo "   • 查看日志: tail -f /var/log/nginx/access.log"
    echo "   • 禁用SELinux启动: ./start_nginx_https.sh --disable-selinux"
    echo ""
    print_status "📌 注意事项:"
    echo "   • 如果遇到访问权限问题，请使用 --disable-selinux 选项"
    echo "   • 自签名证书会导致浏览器安全警告，选择"继续访问"即可"
    echo "   • 确保防火墙已开放端口9443"
    echo -e "\033[1;36m========================================\033[0m"
}

# 主函数
main() {
    print_header
    
    check_root
    
    # 如果指定了禁用SELinux选项
    if [[ "$DISABLE_SELINUX" == "true" ]]; then
        disable_selinux_permanently
    fi
    
    install_dependencies
    generate_ssl_cert
    create_log_dirs
    configure_php_fpm
    configure_nginx
    configure_selinux
    
    # 启动前的最后检查
    print_status "启动前最后检查..."
    
    # 检查关键文件是否存在
    NGINX_CONF="/root/ebpf/nginx-https-server/nginx.conf"
    if [[ ! -f "$NGINX_CONF" ]]; then
        print_error "Nginx配置文件不存在: $NGINX_CONF"
        exit 1
    fi
    
    # 检查SSL证书
    if [[ ! -f "/root/ebpf/nginx-https-server/server.crt" ]] || [[ ! -f "/root/ebpf/nginx-https-server/server.key" ]]; then
        print_error "SSL证书文件不存在"
        exit 1
    fi
    
    # 尝试启动Nginx
    if start_nginx; then
        print_status "Nginx启动成功，检查服务状态..."
        sleep 2
        
        if check_services; then
            show_access_info
        else
            print_error "服务启动后状态检查失败"
            exit 1
        fi
    else
        print_error "Nginx启动失败"
        print_status "请检查错误日志: tail -f /var/log/nginx/error.log"
        print_status "或检查临时错误日志: cat /tmp/nginx_error.log"
        exit 1
    fi
}

# 运行主函数
main "$@"

