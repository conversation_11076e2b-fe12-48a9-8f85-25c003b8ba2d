# eBPF HTTPS 流量分析项目

这是一个基于eBPF技术的HTTPS流量分析项目，用于研究和测试SSL/TLS协议的网络流量分析。

## 项目结构

```
ebpf/
├── https-test-server/          # HTTPS测试服务器
│   ├── https_server.py         # 主要的HTTPS服务器实现
│   ├── test_apis.py           # API接口测试脚本
│   ├── start_https_server.sh  # 服务器启动脚本
│   ├── server.crt             # SSL证书文件
│   ├── server.key             # SSL私钥文件
│   └── README.md              # 详细的服务器说明
└── ssl_key_extractor/          # SSL密钥提取工具
    └── ...                     # eBPF相关工具
```

## 功能模块

### 1. HTTPS测试服务器 (`https-test-server/`)

完整的HTTPS服务器实现，提供多种API接口用于测试：

- 🔒 SSL/TLS加密通信
- 📊 10个不同的API接口
- 📈 包含大数据接口(>2KB)
- 🔧 系统状态监控
- 📝 完整的请求日志

### 2. SSL密钥提取工具 (`ssl_key_extractor/`)

基于eBPF的SSL密钥提取和流量分析工具。

## 快速开始

### 启动HTTPS测试服务器

```bash
cd https-test-server
python3 https_server.py
```

### 测试API接口

```bash
cd https-test-server
python3 test_apis.py
```

## 环境要求

- Linux系统 (支持eBPF)
- Python 3.6+
- 必需的Python库：requests, urllib3, psutil

## 安装依赖

```bash
# 使用国内镜像源安装Python依赖
pip3 install requests urllib3 psutil -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

## 防火墙配置

如果需要从外部访问HTTPS服务器：

```bash
# 开放443端口
firewall-cmd --add-port=443/tcp --permanent
firewall-cmd --reload
```

## 用途

本项目主要用于：

- eBPF HTTPS流量分析研究
- SSL/TLS协议分析和学习
- 网络安全工具开发和测试
- HTTPS协议实现研究
- 网络流量监控技术开发

## 注意事项

- HTTPS服务器使用自签名证书，浏览器会显示安全警告
- 生产环境请使用正式的SSL证书
- 某些eBPF功能需要root权限

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目用于学习和研究目的。 