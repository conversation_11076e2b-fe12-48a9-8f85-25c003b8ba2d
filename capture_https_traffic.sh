#!/bin/bash

echo "=== HTTPS流量抓取工具 ==="
echo "当前时间: $(date)"
echo "网络接口: ens160"
echo "本机IP: *************"
echo "抓取端口: 443, 8443, 9443"
echo ""

# 创建输出目录
mkdir -p network_captures
cd network_captures

echo "选择抓包模式："
echo "1. 抓取所有HTTPS端口流量 (保存到文件)"
echo "2. 实时显示HTTPS端口流量"
echo "3. 抓取特定时间段的流量"
echo "4. 抓取并分析TLS握手"
echo "5. 后台抓包模式"

read -p "请选择模式 (1-5): " choice

case $choice in
    1)
        filename="https_traffic_$(date +%Y%m%d_%H%M%S).pcap"
        echo "开始抓取HTTPS端口流量 (443,8443,9443)，保存到: $filename"
        echo "按 Ctrl+C 停止抓取"
        tcpdump -i ens160 -w "$filename" -s 0 'port 443 or port 8443 or port 9443'
        ;;
    2)
        echo "实时显示HTTPS端口流量 (443,8443,9443) - 按 Ctrl+C 停止"
        tcpdump -i ens160 -n -X 'port 443 or port 8443 or port 9443'
        ;;
    3)
        read -p "请输入抓取时间（秒）: " duration
        filename="https_traffic_${duration}s_$(date +%Y%m%d_%H%M%S).pcap"
        echo "抓取${duration}秒的HTTPS端口流量 (443,8443,9443)..."
        timeout ${duration} tcpdump -i ens160 -w "$filename" -s 0 'port 443 or port 8443 or port 9443'
        echo "抓取完成: $filename"
        ;;
    4)
        echo "抓取并分析TLS握手过程 (443,8443,9443)"
        filename="tls_handshake_$(date +%Y%m%d_%H%M%S).pcap"
        tcpdump -i ens160 -w "$filename" -s 0 -X '(port 443 or port 8443 or port 9443) and (tcp[((tcp[12:1] & 0xf0) >> 2):1] = 0x16)'
        ;;
    5)
        filename="https_background_$(date +%Y%m%d_%H%M%S).pcap"
        echo "启动后台抓包 (443,8443,9443): $filename"
        nohup tcpdump -i ens160 -w "$filename" -s 0 'port 443 or port 8443 or port 9443' > capture.log 2>&1 &
        echo "后台进程ID: $!"
        echo "使用 'ps aux | grep tcpdump' 查看进程"
        echo "使用 'kill PID' 停止抓包"
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo "=== 其他有用的命令 ==="
echo "查看抓包文件: tshark -r filename.pcap"
echo "分析TLS流量: tshark -r filename.pcap -Y 'tls.handshake.type == 1'"
echo "查看HTTP请求: tshark -r filename.pcap -Y 'http.request'"
echo "按端口分析: tshark -r filename.pcap -Y 'tcp.port == 9443'" 