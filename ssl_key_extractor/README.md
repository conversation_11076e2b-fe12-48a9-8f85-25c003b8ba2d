# SSL密钥提取器 - 企业级HTTPS解密方案

## 🚀 核心特性

- **全局透明注入**：无需修改应用启动参数，系统级自动监控
- **生产环境友好**：完全兼容systemd服务和现有部署
- **客户环境零侵入**：无需修改nginx、apache等应用配置
- **实时密钥提取**：支持TLS 1.2/1.3，兼容Wireshark解密

## 📦 文件结构

```
ssl_key_extractor/
├── src/
│   └── keylog_injector.c          # SSL注入器源码
├── setup_global_injection.sh      # 全局注入管理脚本
├── libkeylog_injector.so          # 编译的注入器库
├── Makefile                       # 构建系统
├── README.md                      # 本文档
└── LICENSE                        # 许可证
```

## 🛠️ 快速开始

### 1. 编译

```bash
make
```

### 2. 启用全局注入

```bash
# 启用全局SSL密钥提取（需要root权限）
sudo make global-enable

# 或者直接使用脚本
sudo ./setup_global_injection.sh enable
```

### 3. 测试

```bash
# 重启nginx测试
sudo systemctl restart nginx

# 访问HTTPS服务
curl https://example.com

# 查看提取的密钥
cat /tmp/global_ssl_keylog.txt
```

## 🎯 支持情况

### ✅ **完全支持的应用**
- **nginx** - Web服务器和反向代理 (使用OpenSSL)
- **apache httpd** - Web服务器 (使用OpenSSL)
- **curl** - 命令行HTTP客户端 (使用OpenSSL)
- **Python requests** - 基于OpenSSL的HTTP库
- **任何直接调用OpenSSL函数的C/C++应用**

### ❌ **不支持的应用**
- **Java/Tomcat** - 使用JSSE (Java Secure Socket Extension)，不调用OpenSSL的`SSL_CTX_new`函数
- **Node.js** - 使用内置的TLS实现，不直接调用OpenSSL函数
- **Go应用** - 使用Go标准库的crypto/tls包
- **.NET Core应用** - 使用.NET的SSL/TLS实现
- **大多数现代编程语言的内置SSL实现**

### 🔧 **技术限制说明**
本SSL密钥提取器基于**OpenSSL函数拦截**技术：
- ✅ 拦截`SSL_CTX_new`、`SSL_CTX_set_keylog_callback`等OpenSSL函数
- ❌ 无法拦截各编程语言的原生SSL/TLS实现
- ❌ 无法处理JVM、Node.js、Go等运行时的内置SSL库

### 🆘 **为不支持的应用解决方案**
1. **反向代理方案**：在应用前部署nginx/apache作为HTTPS终端
2. **网络层抓包**：使用eBPF、tcpdump等网络层工具
3. **应用层修改**：修改应用代码输出SSL密钥
4. **容器/虚拟化**：通过网络层拦截SSL流量

### ✅ 部署环境
- **systemd服务** - 完美兼容
- **Docker容器** - 支持
- **生产环境** - 零侵入部署

## 📋 命令参考

### 全局注入管理

```bash
# 启用全局注入
make global-enable
sudo ./setup_global_injection.sh enable

# 查看状态
make global-status
./setup_global_injection.sh status

# 测试功能
make global-test
./setup_global_injection.sh test

# 禁用全局注入
make global-disable
sudo ./setup_global_injection.sh disable
```

### 传统使用方式

```bash
# 单次使用
LD_PRELOAD=./libkeylog_injector.so your_app

# 环境变量方式
export LD_PRELOAD=./libkeylog_injector.so
your_app
```

## 🔧 工作原理

### 全局注入机制
1. **系统级预加载**：通过`/etc/ld.so.preload`实现
2. **配置文件驱动**：使用`/etc/ssl-keylog.conf`配置
3. **OpenSSL函数拦截**：拦截`SSL_CTX_new`等关键函数
4. **密钥回调设置**：自动设置keylog回调函数

### SSL密钥提取流程
```
应用启动 → 自动加载注入器 → 拦截OpenSSL函数 → 设置密钥回调 → 提取密钥
```

**⚠️ 注意**：只有当应用直接调用OpenSSL库函数时才能成功拦截！

## 📊 配置选项

### 全局配置文件：`/etc/ssl-keylog.conf`

```ini
# SSL密钥输出文件路径
KEYLOG_FILE=/tmp/global_ssl_keylog.txt

# 静默模式（1=启用，0=禁用）
KEYLOG_SILENT=1

# 详细日志（仅调试时启用）
KEYLOG_ENABLE_DETAILED_LOG=0
```

## 🧪 测试验证

### nginx测试
```bash
# 启用全局注入
sudo make global-enable

# 重启nginx
sudo systemctl restart nginx

# 访问HTTPS服务
curl -k https://localhost:443/

# 查看提取的密钥
cat /tmp/global_ssl_keylog.txt
```

### curl测试
```bash
# 执行HTTPS请求
curl https://httpbin.org/get

# 检查密钥文件
wc -l /tmp/global_ssl_keylog.txt
```

## 🔍 故障排除

### 1. 检查注入状态
```bash
./setup_global_injection.sh status
```

### 2. 检查进程加载
```bash
# 查看nginx进程是否加载了注入器
pmap $(pgrep nginx) | grep keylog
```

### 3. 检查密钥文件
```bash
# 查看密钥文件状态
ls -la /tmp/global_ssl_keylog.txt
tail -5 /tmp/global_ssl_keylog.txt
```

### 4. 测试连接
```bash
# 测试全局注入
./setup_global_injection.sh test
```

### 5. 验证应用兼容性
```bash
# 启用详细日志
export KEYLOG_ENABLE_DETAILED_LOG=1

# 查看是否调用了SSL_CTX_new函数
grep "SSL_CTX_new" /tmp/keylog_injector.log

# 如果没有相关日志，说明应用不使用OpenSSL
```

## ⚠️ 重要说明

### 应用兼容性
- **仅支持直接使用OpenSSL的应用**
- **Java、Node.js、Go等现代应用通常不支持**
- **建议先测试验证再部署到生产环境**

### SSL会话重用
- **浏览器会话重用**：同一浏览器实例（包括无痕模式）会重用SSL会话
- **强制新握手**：需要完全退出浏览器再重新访问
- **nginx配置影响**：`ssl_session_cache`和`keepalive_timeout`会影响密钥生成

### 安全考虑
- 密钥文件权限设置为600（仅owner可读）
- 建议在测试环境使用
- 生产环境需要评估安全风险

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

> **关键优势**：专门针对OpenSSL应用设计的全局注入方案，无需修改应用启动参数！
> 
> **重要提醒**：使用前请务必验证目标应用的SSL实现方式！ 